class ApiConfig {
  static const String baseUrl = 'https://25ab18bc59c2.ngrok-free.app';
  static const String loginUrl = '$baseUrl/api/login';
  static const String registerUrl = '$baseUrl/api/register';

  static String getErrorMessage(int statusCode) {
    switch (statusCode) {
      case 401:
        return 'Email ou mot de passe incorrect';
      case 404:
        return 'Service non disponible';
      case 409:
        return 'Un compte avec cet email existe déjà';
      case 500:
        return 'Erreur serveur interne';
      default:
        return 'Une erreur inconnue est survenue';
    }
  }

  static String getConnectionErrorMessage(String error) {
    if (error.contains('SocketException')) {
      return 'Impossible de se connecter au serveur. Vérifiez votre connexion Internet.';
    } else if (error.contains('TimeoutException')) {
      return 'Délai d\'attente dépassé. Réessayez plus tard.';
    }
    return 'Erreur de connexion : $error';
  }
}